import { AccountType } from '../../../types/websocket/enums'

class Auth {
  private static instance: Auth
  private authPayload: WSAuthPayload

  private constructor() {
    this.authPayload = {
      session: 'd4mshsf11u1peock1js2no9qmd',
      isDemo: AccountType.Demo,
      uid: *********,
      platform: 0,
      isFastHistory: false
    }
  }

  public static getInstance(): Auth {
    if (!Auth.instance) {
      Auth.instance = new Auth()
    }
    return Auth.instance
  }

  getAuthPayload(): WSAuthPayload {
    return this.authPayload
  }
}

export default Auth
