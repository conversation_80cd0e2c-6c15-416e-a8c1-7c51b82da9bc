export const formatPayload = (payload: unknown): unknown | null => {
  try {
    if (payload instanceof Buffer || payload instanceof ArrayBuffer) {
      const buffer = payload as Buffer
      const jsonStr = buffer.toString('utf-8')
      const jsonData = JSON.parse(jsonStr)

      return jsonData
    }

    return null
  } catch (error) {
    console.error('Error parsing payload:', error)
    return null
  }
}
