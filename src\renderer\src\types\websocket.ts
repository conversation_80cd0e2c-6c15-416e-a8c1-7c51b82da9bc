/**
 * Authentication payload structure for WebSocket connection
 */
export interface AuthPayload {
  /** Session identifier */
  session: string
  /** Demo mode flag (0 = false, 1 = true) */
  isDemo: number
  /** User identifier */
  uid: string
  /** Platform identifier */
  platform: number
  /** Fast history flag */
  isFastHistory: boolean
}

/**
 * WebSocket client configuration options
 */
export interface WebSocketConfig {
  /** Server URL to connect to */
  serverUrl: string
  /** Authentication payload */
  auth: AuthPayload
  /** Connection timeout in milliseconds (default: 5000) */
  timeout?: number
  /** Auto-reconnect flag (default: true) */
  autoReconnect?: boolean
  /** Maximum reconnection attempts (default: 5) */
  maxReconnectAttempts?: number
  /** Reconnection delay in milliseconds (default: 1000) */
  reconnectDelay?: number
}

/**
 * WebSocket connection states
 */
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * WebSocket event types
 */
export interface WebSocketEvents {
  /** Connection established */
  connect: () => void
  /** Connection lost */
  disconnect: (reason: string) => void
  /** Connection error */
  error: (error: Error) => void
  /** Reconnection attempt */
  reconnect: (attemptNumber: number) => void
  /** Reconnection failed */
  reconnect_failed: () => void
  /** Custom message received */
  message: (data: unknown) => void
}

/**
 * WebSocket client interface
 */
export interface IWebSocketClient {
  /** Current connection state */
  readonly state: ConnectionState
  /** Connect to the server */
  connect(): Promise<void>
  /** Disconnect from the server */
  disconnect(): void
  /** Send a message to the server */
  send(event: string, data?: unknown): void
  /** Listen for events */
  on<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void
  /** Remove event listener */
  off<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void
  /** Check if connected */
  isConnected(): boolean
}
