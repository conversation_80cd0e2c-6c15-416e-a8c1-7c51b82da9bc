import { useEffect, useState } from 'react'
import Versions from './components/Versions'

import electronLogo from './assets/electron.svg'

function App(): React.JSX.Element {
  const [activeTab, setActiveTab] = useState<'home' | 'websocket'>('home')
  const ipcHandle = (): void => window.electron.ipcRenderer.send('ping')

  useEffect(() => {
    window.api.on('ws:event', (event, data) => {
      console.log('Received event:', event, data)
    })
  }, [])

  const tabStyle = {
    padding: '10px 20px',
    margin: '0 5px',
    border: 'none',
    borderRadius: '5px 5px 0 0',
    cursor: 'pointer',
    backgroundColor: '#f0f0f0',
    color: '#333'
  }

  const activeTabStyle = {
    ...tabStyle,
    backgroundColor: '#007bff',
    color: 'white'
  }

  return (
    <>
      <div style={{ marginBottom: '20px' }}>
        <button
          style={activeTab === 'home' ? activeTabStyle : tabStyle}
          onClick={() => setActiveTab('home')}
        >
          Home
        </button>
        <button
          style={activeTab === 'websocket' ? activeTabStyle : tabStyle}
          onClick={() => setActiveTab('websocket')}
        >
          WebSocket Client
        </button>
      </div>

      {activeTab === 'home' && (
        <>
          <img alt="logo" className="logo" src={electronLogo} />
          <div className="creator">Powered by electron-vite</div>
          <div className="text">
            Build an Electron app with <span className="react">React</span>
            &nbsp;and <span className="ts">TypeScript</span>
          </div>
          <p className="tip">
            Please try pressing <code>F12</code> to open the devTool
          </p>
          <div className="actions">
            <div className="action">
              <a href="https://electron-vite.org/" target="_blank" rel="noreferrer">
                Documentation
              </a>
            </div>
            <div className="action">
              <a target="_blank" rel="noreferrer" onClick={ipcHandle}>
                Send IPC
              </a>
            </div>
          </div>
          <Versions></Versions>
        </>
      )}
    </>
  )
}

export default App
