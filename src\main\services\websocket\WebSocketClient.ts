import { WSConnectionState } from '../../../types/websocket/enums'
import { io, Socket } from 'socket.io-client'

import { formatPayload } from '../../../utils/payload'
import { BrowserWindow } from 'electron'

import Auth from './Auth'

enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

interface WSError {
  code: string
  message: string
  timestamp: number
  context?: unknown
}

interface PendingEventListener {
  event: string
  listener: (...args: unknown[]) => void
  once?: boolean
}

interface BufferedEvent {
  event: string
  data: unknown
  timestamp: number
  retryCount: number
}

interface ConnectionMetrics {
  connectAttempts: number
  lastConnectTime: number | null
  lastDisconnectTime: number | null
  totalReconnects: number
  lastHeartbeatSent: number | null
  lastHeartbeatReceived: number | null
  averageLatency: number
  connectionUptime: number
}

interface HealthStatus {
  isHealthy: boolean
  latency: number | null
  uptime: number
  lastHeartbeat: number | null
  issues: string[]
}

class WebSocketClient {
  private static instance: WebSocketClient
  private socket: Socket | null = null
  private auth: Auth

  private currentState: WSConnectionState = WSConnectionState.DISCONNECTED
  private heartbeatInterval: NodeJS.Timeout | null = null
  private reconnectTimeout: NodeJS.Timeout | null = null

  // Event listener queue for disconnected state
  private pendingListeners: PendingEventListener[] = []
  private attachedListeners: Set<string> = new Set()

  // Event buffering for disconnected state
  private eventBuffer: BufferedEvent[] = []
  private readonly MAX_BUFFER_SIZE = 100
  private readonly MAX_RETRY_COUNT = 3
  private readonly BUFFER_TIMEOUT = 30000 // 30 seconds

  // Connection management
  private connectionMetrics: ConnectionMetrics = {
    connectAttempts: 0,
    lastConnectTime: null,
    lastDisconnectTime: null,
    totalReconnects: 0,
    lastHeartbeatSent: null,
    lastHeartbeatReceived: null,
    averageLatency: 0,
    connectionUptime: 0
  }

  // Health monitoring
  private healthCheckInterval: NodeJS.Timeout | null = null
  private readonly HEALTH_CHECK_INTERVAL = 30000 // 30 seconds
  private readonly MAX_LATENCY_THRESHOLD = 5000 // 5 seconds
  private readonly HEARTBEAT_TIMEOUT = 60000 // 60 seconds

  // Error tracking
  private lastError: WSError | null = null
  private errorHistory: WSError[] = []
  private readonly MAX_ERROR_HISTORY = 10

  private readonly SOCKET_URL = 'wss://demo-api-eu.po.market'
  private readonly SOCKET_ORIGIN = 'https://pocketoption.com'
  private readonly HEARTBEAT = 20000 // 20 seconds
  private readonly MAX_RECONNECT_ATTEMPTS = 5
  private readonly RECONNECT_DELAY = 1000 // 1 second base delay

  private balanceInfo: WSAccountBalance | null = null
  private chartSettings: WSChartSettings | null = null

  private constructor() {
    this.auth = Auth.getInstance()
    this.connect()
  }

  public static getInstance(): WebSocketClient {
    if (!WebSocketClient.instance) {
      WebSocketClient.instance = new WebSocketClient()
    }
    return WebSocketClient.instance
  }

  getConnectionStatus(): WSConnectionState {
    return this.currentState
  }

  connect(): void {
    if (
      this.currentState === WSConnectionState.CONNECTED ||
      this.currentState === WSConnectionState.CONNECTING
    ) {
      this.log(LogLevel.DEBUG, `Already ${this.currentState}, skipping connect`)
      return
    }

    // Clear any existing reconnect timeout
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }

    this.connectionMetrics.connectAttempts++
    this.setConnectionState(WSConnectionState.CONNECTING)

    this.log(LogLevel.INFO, `Connecting to WebSocket`, {
      attempt: this.connectionMetrics.connectAttempts,
      url: this.SOCKET_URL
    })

    try {
      const options = {
        transports: ['websocket'],
        query: {
          EIO: '4',
          transport: ['websocket']
        }
      }

      this.socket = io(this.SOCKET_URL, {
        ...options,
        extraHeaders: {
          Origin: this.SOCKET_ORIGIN
        },
        path: '/socket.io/'
      })

      this.setupEventHandlers()
    } catch (error) {
      this.handleError('CONNECTION_SETUP_FAILED', 'Failed to setup WebSocket connection', error)
      this.setConnectionState(WSConnectionState.ERROR)
    }
  }

  disconnect(): void {
    this.log(LogLevel.INFO, 'Manually disconnecting WebSocket...')

    // Clear reconnect timeout to prevent automatic reconnection
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }

    this.clearAttachedListeners()
    this.stopHeartbeat()

    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }

    this.setConnectionState(WSConnectionState.DISCONNECTED)
  }

  isConnected(): boolean {
    return this.currentState === WSConnectionState.CONNECTED
  }

  emit(event: string, ...args: unknown[]): void {
    if (!this.socket || !this.isConnected()) return

    const payload = args.length > 0 ? args[0] : args

    if (payload === undefined) {
      this.socket.emit(event)
    } else {
      this.socket.emit(event, payload)
    }
  }

  send(event: string, data?: unknown): void {
    if (!this.isConnected()) {
      // Buffer the event for later sending
      this.bufferEvent(event, data)
      this.log(LogLevel.WARN, `WebSocket not connected, buffering event [${event}]`, {
        event,
        data
      })
      return
    }

    if (!this.socket) {
      this.handleError('SOCKET_NOT_AVAILABLE', 'Cannot send message: Socket is not available', {
        event,
        data
      })
      return
    }

    try {
      this.log(LogLevel.DEBUG, `Sending message [${event}]`, data)
      this.socket.emit(event, data)
    } catch (error) {
      this.handleError('MESSAGE_SEND_FAILED', `Failed to send message [${event}]`, {
        event,
        data,
        error
      })
    }
  }

  on<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void {
    const eventStr = String(event)
    const wrappedListener = (...args: unknown[]) => {
      try {
        const formattedArgs = args.map((arg) => formatPayload(arg))

        if (typeof listener === 'function') {
          // Cast the listener to accept any args to bypass the tuple restriction
          ;(listener as (...args: unknown[]) => void)(...formattedArgs)
        }
      } catch (error) {
        console.error(`Error processing event "${eventStr}":`, error)
      }
    }

    // If socket is connected and authenticated, attach immediately
    if (this.socket && this.isConnected()) {
      this.socket.on(eventStr, wrappedListener)
      this.attachedListeners.add(eventStr)
      console.log(`Event listener attached immediately for: ${eventStr}`)
    } else {
      // Queue the listener for later attachment
      this.pendingListeners.push({
        event: eventStr,
        listener: wrappedListener,
        once: false
      })
      console.log(`Event listener queued for: ${eventStr} (socket not ready)`)
    }
  }

  once(event: string, listener: WSCallback): void {
    const wrappedListener = (...args: unknown[]): void => {
      try {
        const formattedArgs = args.map((arg) => formatPayload(arg))
        listener(...formattedArgs)
      } catch (error) {
        console.error(`Error processing once event "${event}":`, error)
      }
    }

    // If socket is connected and authenticated, attach immediately
    if (this.socket && this.isConnected()) {
      this.socket.once(event, wrappedListener)
      console.log(`Once listener attached immediately for: ${event}`)
    } else {
      // Queue the listener for later attachment
      this.pendingListeners.push({
        event,
        listener: wrappedListener,
        once: true
      })
      console.log(`Once listener queued for: ${event} (socket not ready)`)
    }
  }

  getChartSettings(): WSChartSettings | null {
    return this.chartSettings
  }

  getAccountBalance(): WSAccountBalance | null {
    return this.balanceInfo
  }

  /**
   * Get connection metrics for monitoring
   */
  getConnectionMetrics(): ConnectionMetrics {
    return { ...this.connectionMetrics }
  }

  /**
   * Get last error information
   */
  getLastError(): WSError | null {
    return this.lastError
  }

  /**
   * Get error history
   */
  getErrorHistory(): WSError[] {
    return [...this.errorHistory]
  }

  /**
   * Get current health status
   */
  getHealthStatus(): HealthStatus {
    const now = Date.now()
    const issues: string[] = []

    // Check connection uptime
    const uptime = this.connectionMetrics.lastConnectTime
      ? now - this.connectionMetrics.lastConnectTime
      : 0

    // Check heartbeat health
    let lastHeartbeat = this.connectionMetrics.lastHeartbeatReceived
    if (this.connectionMetrics.lastHeartbeatSent && !lastHeartbeat) {
      lastHeartbeat = this.connectionMetrics.lastHeartbeatSent
    }

    if (lastHeartbeat && now - lastHeartbeat > this.HEARTBEAT_TIMEOUT) {
      issues.push('Heartbeat timeout detected')
    }

    // Check latency
    const latency = this.connectionMetrics.averageLatency
    if (latency > this.MAX_LATENCY_THRESHOLD) {
      issues.push(`High latency detected: ${latency}ms`)
    }

    // Check connection state
    if (this.currentState !== WSConnectionState.CONNECTED) {
      issues.push(`Connection not established: ${this.currentState}`)
    }

    return {
      isHealthy: issues.length === 0 && this.isConnected(),
      latency: latency > 0 ? latency : null,
      uptime,
      lastHeartbeat,
      issues
    }
  }

  private startHeartbeat(): void {
    this.stopHeartbeat()
    this.heartbeatInterval = setInterval(() => {
      if (this.socket && this.socket.connected) {
        const heartbeatTime = Date.now()
        this.connectionMetrics.lastHeartbeatSent = heartbeatTime

        this.socket.emit('ps')
        this.log(LogLevel.DEBUG, 'Heartbeat sent')

        // Periodically cleanup expired buffered events
        this.cleanupEventBuffer()

        // Update connection uptime
        if (this.connectionMetrics.lastConnectTime) {
          this.connectionMetrics.connectionUptime =
            heartbeatTime - this.connectionMetrics.lastConnectTime
        }
      }
    }, this.HEARTBEAT)

    // Start health monitoring
    this.startHealthMonitoring()
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
    this.stopHealthMonitoring()
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    this.stopHealthMonitoring()

    this.healthCheckInterval = setInterval(() => {
      const health = this.getHealthStatus()

      if (!health.isHealthy) {
        this.log(LogLevel.WARN, 'Connection health issues detected', health)

        // Broadcast health status to renderer
        this.broadcast('ws:health', health)

        // Take corrective action for severe issues
        if (health.issues.includes('Heartbeat timeout detected')) {
          this.log(LogLevel.ERROR, 'Heartbeat timeout - attempting reconnection')
          this.scheduleReconnect()
        }
      } else {
        this.log(LogLevel.DEBUG, 'Connection health check passed', {
          latency: health.latency,
          uptime: health.uptime
        })
      }
    }, this.HEALTH_CHECK_INTERVAL)
  }

  /**
   * Stop health monitoring
   */
  private stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }
  }

  private setConnectionState(state: WSConnectionState): void {
    const previousState = this.currentState
    this.currentState = state

    console.log(`WebSocket state changed: ${previousState} -> ${state}`)

    // Update connection metrics
    if (state === WSConnectionState.CONNECTED) {
      this.connectionMetrics.lastConnectTime = Date.now()
      this.connectionMetrics.connectAttempts = 0
    } else if (state === WSConnectionState.DISCONNECTED) {
      this.connectionMetrics.lastDisconnectTime = Date.now()
    }
  }

  /**
   * Process pending event listeners when connection is established
   */
  private processPendingListeners(): void {
    if (!this.socket || !this.isConnected()) {
      console.warn('Cannot process pending listeners - socket not ready')
      return
    }

    console.log(`Processing ${this.pendingListeners.length} pending event listeners`)

    const listenersToProcess = [...this.pendingListeners]
    this.pendingListeners = []

    listenersToProcess.forEach(({ event, listener, once }) => {
      try {
        if (once) {
          this.socket!.once(event, listener)
          console.log(`Attached pending once listener for: ${event}`)
        } else {
          this.socket!.on(event, listener)
          this.attachedListeners.add(event)
          console.log(`Attached pending listener for: ${event}`)
        }
      } catch (error) {
        console.error(`Failed to attach pending listener for ${event}:`, error)
        // Re-queue the listener for retry
        this.pendingListeners.push({ event, listener, once })
      }
    })
  }

  /**
   * Clear all attached event listeners
   */
  private clearAttachedListeners(): void {
    if (this.socket) {
      this.attachedListeners.forEach((event) => {
        this.socket!.removeAllListeners(event)
      })
    }
    this.attachedListeners.clear()
    console.log('Cleared all attached event listeners')
  }

  /**
   * Log message with level and context
   */
  private log(level: LogLevel, message: string, context?: unknown): void {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [WebSocket] [${level}] ${message}`

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(logMessage, context || '')
        break
      case LogLevel.INFO:
        console.log(logMessage, context || '')
        break
      case LogLevel.WARN:
        console.warn(logMessage, context || '')
        break
      case LogLevel.ERROR:
        console.error(logMessage, context || '')
        break
    }

    // Broadcast important logs to renderer
    if (level === LogLevel.ERROR || level === LogLevel.WARN) {
      this.broadcast('ws:log', { level, message, context, timestamp })
    }
  }

  /**
   * Record and handle errors
   */
  private handleError(code: string, message: string, context?: unknown): void {
    const error: WSError = {
      code,
      message,
      timestamp: Date.now(),
      context
    }

    this.lastError = error
    this.errorHistory.push(error)

    // Keep error history within limits
    if (this.errorHistory.length > this.MAX_ERROR_HISTORY) {
      this.errorHistory.shift()
    }

    this.log(LogLevel.ERROR, `${code}: ${message}`, context)
    this.broadcast('ws:error', error)
  }

  /**
   * Buffer an event for later sending when connection is restored
   */
  private bufferEvent(event: string, data: unknown): void {
    // Don't buffer certain system events
    const systemEvents = ['auth', 'ps', 'ping', 'pong']
    if (systemEvents.includes(event)) {
      return
    }

    const bufferedEvent: BufferedEvent = {
      event,
      data,
      timestamp: Date.now(),
      retryCount: 0
    }

    this.eventBuffer.push(bufferedEvent)

    // Maintain buffer size limit
    if (this.eventBuffer.length > this.MAX_BUFFER_SIZE) {
      const removed = this.eventBuffer.shift()
      this.log(LogLevel.WARN, 'Event buffer full, removing oldest event', removed)
    }

    this.log(LogLevel.DEBUG, `Buffered event [${event}]`, {
      bufferSize: this.eventBuffer.length,
      maxSize: this.MAX_BUFFER_SIZE
    })
  }

  /**
   * Process buffered events when connection is restored
   */
  private processBufferedEvents(): void {
    if (this.eventBuffer.length === 0) {
      return
    }

    this.log(LogLevel.INFO, `Processing ${this.eventBuffer.length} buffered events`)

    const eventsToProcess = [...this.eventBuffer]
    this.eventBuffer = []

    eventsToProcess.forEach((bufferedEvent) => {
      const { event, data, timestamp, retryCount } = bufferedEvent

      // Check if event is too old
      if (Date.now() - timestamp > this.BUFFER_TIMEOUT) {
        this.log(LogLevel.WARN, `Discarding expired buffered event [${event}]`, {
          age: Date.now() - timestamp,
          timeout: this.BUFFER_TIMEOUT
        })
        return
      }

      // Check retry count
      if (retryCount >= this.MAX_RETRY_COUNT) {
        this.log(LogLevel.ERROR, `Discarding buffered event [${event}] after max retries`, {
          retryCount,
          maxRetries: this.MAX_RETRY_COUNT
        })
        return
      }

      // Try to send the event
      if (this.socket && this.isConnected()) {
        try {
          this.socket.emit(event, data)
          this.log(LogLevel.DEBUG, `Successfully sent buffered event [${event}]`)
        } catch (error) {
          // Re-buffer with incremented retry count
          this.eventBuffer.push({
            ...bufferedEvent,
            retryCount: retryCount + 1
          })
          this.log(LogLevel.WARN, `Failed to send buffered event [${event}], re-buffering`, error)
        }
      } else {
        // Re-buffer if connection is lost again
        this.eventBuffer.push(bufferedEvent)
      }
    })
  }

  /**
   * Clear expired events from buffer
   */
  private cleanupEventBuffer(): void {
    const now = Date.now()
    const initialSize = this.eventBuffer.length

    this.eventBuffer = this.eventBuffer.filter((event) => {
      return now - event.timestamp <= this.BUFFER_TIMEOUT
    })

    const removedCount = initialSize - this.eventBuffer.length
    if (removedCount > 0) {
      this.log(LogLevel.DEBUG, `Cleaned up ${removedCount} expired events from buffer`)
    }
  }

  /**
   * Schedule automatic reconnection with exponential backoff
   */
  private scheduleReconnect(): void {
    // Don't reconnect if we're already connected or connecting
    if (
      this.currentState === WSConnectionState.CONNECTED ||
      this.currentState === WSConnectionState.CONNECTING
    ) {
      return
    }

    // Don't reconnect if we've exceeded max attempts
    if (this.connectionMetrics.connectAttempts >= this.MAX_RECONNECT_ATTEMPTS) {
      this.handleError(
        'MAX_RECONNECT_ATTEMPTS_EXCEEDED',
        `Max reconnection attempts (${this.MAX_RECONNECT_ATTEMPTS}) exceeded`,
        { attempts: this.connectionMetrics.connectAttempts }
      )
      this.setConnectionState(WSConnectionState.ERROR)
      this.broadcast('reconnect_failed')
      return
    }

    // Clear any existing reconnect timeout
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
    }

    // Calculate delay with exponential backoff
    const delay = this.RECONNECT_DELAY * Math.pow(2, this.connectionMetrics.connectAttempts - 1)

    this.log(LogLevel.INFO, `Scheduling reconnection in ${delay}ms`, {
      attempt: this.connectionMetrics.connectAttempts + 1,
      maxAttempts: this.MAX_RECONNECT_ATTEMPTS,
      delay
    })

    this.setConnectionState(WSConnectionState.RECONNECTING)
    this.connectionMetrics.totalReconnects++

    this.reconnectTimeout = setTimeout(() => {
      this.reconnectTimeout = null
      this.log(LogLevel.INFO, 'Attempting automatic reconnection...')
      this.connect()
    }, delay)
  }

  private setupEventHandlers(): void {
    if (!this.socket) return

    // Critical connection event handlers - attached immediately
    this.socket.on('connect', () => {
      console.log('Socket connected, sending auth...')
      this.socket!.emit('auth', this.auth.getAuthPayload())
      // Don't set CONNECTED state yet - wait for successauth
    })

    this.socket.on('successauth', () => {
      this.log(LogLevel.INFO, 'Authentication successful')
      this.setConnectionState(WSConnectionState.CONNECTED)

      // Start heartbeat to maintain connection
      this.startHeartbeat()

      // Process any pending event listeners now that we're fully connected
      this.processPendingListeners()

      // Process any buffered events
      this.processBufferedEvents()

      this.broadcast(`connected`)
    })

    this.socket.on('disconnect', (...args: unknown[]) => {
      console.log('Socket disconnected:', args)
      this.setConnectionState(WSConnectionState.DISCONNECTED)

      // Clear attached listeners
      this.clearAttachedListeners()

      this.stopHeartbeat()
      this.broadcast(`disconnected`, args)

      // Attempt reconnection if not manually disconnected
      this.scheduleReconnect()
    })

    this.socket.on('connect_error', (...args: unknown[]) => {
      console.error('Socket connection error:', args)
      this.setConnectionState(WSConnectionState.ERROR)

      this.clearAttachedListeners()
      this.stopHeartbeat()

      this.broadcast(`error`, args)

      // Attempt reconnection on connection error
      this.scheduleReconnect()
    })

    // Handle heartbeat response for latency calculation
    this.socket.on('ps', () => {
      if (this.connectionMetrics.lastHeartbeatSent) {
        const latency = Date.now() - this.connectionMetrics.lastHeartbeatSent
        this.connectionMetrics.lastHeartbeatReceived = Date.now()

        // Calculate rolling average latency
        if (this.connectionMetrics.averageLatency === 0) {
          this.connectionMetrics.averageLatency = latency
        } else {
          this.connectionMetrics.averageLatency =
            this.connectionMetrics.averageLatency * 0.8 + latency * 0.2
        }

        this.log(LogLevel.DEBUG, `Heartbeat response received`, {
          latency,
          averageLatency: Math.round(this.connectionMetrics.averageLatency)
        })
      }
    })

    // Application-level event handlers - use the queue system
    this.on('successupdateBalance', (data) => {
      this.log(LogLevel.DEBUG, 'Balance update received', data)
      this.balanceInfo = data as WSAccountBalance
      this.broadcast(`balance`, data)
    })

    this.on('updateAssets', (data) => {
      this.log(LogLevel.DEBUG, 'Assets update received', data)
      this.chartSettings = data as WSChartSettings
      this.broadcast(`chartSettings`, data)
    })

    // Broadcast all events to renderer
    this.socket.onAny((event: string, ...args: unknown[]) => {
      const payload = args.length > 0 ? args[0] : args
      const data = formatPayload(payload)
      this.broadcast(event, data)
    })
  }

  /**
   * Send events to renderer via IPC
   * @param event Event name
   * @param data Event data
   */
  private broadcast(event: string, data?: unknown): void {
    let payload: unknown
    BrowserWindow.getAllWindows().forEach((w) => {
      if (data instanceof Buffer || data instanceof ArrayBuffer) {
        payload = formatPayload(data)
      } else {
        payload = data
      }
      w.webContents.send('ws:event', event, payload)
    })
  }
}

export default WebSocketClient
