import { WSConnectionState } from '../../../types/websocket/enums'
import { io, Socket } from 'socket.io-client'

import { formatPayload } from '../../../utils/payload'
import { BrowserWindow } from 'electron'

import Auth from './Auth'

class WebSocketClient {
  private static instance: WebSocketClient
  private socket: Socket | null = null
  private auth: Auth

  private currentState: WSConnectionState = WSConnectionState.DISCONNECTED
  private heartbeatInterval: NodeJS.Timeout | null = null

  private readonly SOCKET_URL = 'wss://demo-api-eu.po.market'
  private readonly SOCKET_ORIGIN = 'https://pocketoption.com'
  private readonly HEARTBEAT = 20000 // 20 seconds

  private balanceInfo: WSAccountBalance | null = null
  private chartSettings: WSChartSettings | null = null

  private constructor() {
    this.auth = Auth.getInstance()
    this.connect()
  }

  public static getInstance(): WebSocketClient {
    if (!WebSocketClient.instance) {
      WebSocketClient.instance = new WebSocketClient()
    }
    return WebSocketClient.instance
  }

  getConnectionStatus(): WSConnectionState {
    return this.currentState
  }

  connect(): void {
    if (this.currentState === WSConnectionState.CONNECTED) {
      return
    }

    this.setConnectionState(WSConnectionState.CONNECTING)

    const options = {
      transports: ['websocket'],
      query: {
        EIO: '4',
        transport: ['websocket']
      }
    }

    this.socket = io(this.SOCKET_URL, {
      ...options,
      extraHeaders: {
        Origin: this.SOCKET_ORIGIN
      },
      path: '/socket.io/'
    })

    this.setupEventHandlers()

    this.socket.once('connect', () => {
      this.socket!.emit('auth', this.auth.getAuthPayload())
      this.setConnectionState(WSConnectionState.CONNECTED)
      this.startHeartbeat()
    })
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }

    this.setConnectionState(WSConnectionState.DISCONNECTED)
    this.stopHeartbeat()
  }

  isConnected(): boolean {
    return this.currentState === WSConnectionState.CONNECTED
  }

  emit(event: string, ...args: unknown[]): void {
    if (!this.socket || !this.isConnected()) return

    const payload = args.length > 0 ? args[0] : args

    if (payload === undefined) {
      this.socket.emit(event)
    } else {
      this.socket.emit(event, payload)
    }
  }

  send(event: string, data?: unknown): void {
    if (!this.isConnected()) {
      console.warn('Cannot send message: WebSocket is not connected')
      return
    }

    if (!this.socket) {
      console.warn('Cannot send message: Socket is not available')
      return
    }

    console.log(`Sending message [${event}]:`, data)
    this.socket.emit(event, data)
  }

  on<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void {
    if (!this.socket || !this.isConnected()) {
      console.warn(`Cannot attach listener to event "${String(event)}" - socket not connected`)
      return
    }

    this.socket.on(event as string, (...args: unknown[]) => {
      try {
        const formattedArgs = args.map((arg) => formatPayload(arg))

        if (typeof listener === 'function') {
          // Cast the listener to accept any args to bypass the tuple restriction
          ;(listener as (...args: unknown[]) => void)(...formattedArgs)
        }
      } catch (error) {
        console.error(`Error processing event "${String(event)}":`, error)
      }
    })
  }

  once(event: string, listener: WSCallback): void {
    if (!this.socket || !this.isConnected()) return
    this.socket.once(event, listener)
  }

  getChartSettings(): WSChartSettings | null {
    return this.chartSettings
  }

  getAccountBalance(): WSAccountBalance | null {
    return this.balanceInfo
  }

  private startHeartbeat(): void {
    this.stopHeartbeat()
    this.heartbeatInterval = setInterval(() => {
      if (this.socket && this.socket.connected) {
        this.socket.emit('ps')
        console.log(`Heartbeat sent`)
      }
    }, this.HEARTBEAT)
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  private setConnectionState(state: WSConnectionState): void {
    this.currentState = state
  }

  private setupEventHandlers(): void {
    if (!this.socket) return

    this.socket.on('successauth', () => {
      this.setConnectionState(WSConnectionState.CONNECTED)

      this.broadcast(`connected`)
    })

    this.on('successupdateBalance', (data) => {
      console.log(`BALANCE DATA:`, data)
    })
    // this.socket.on('successupdateBalance', (data) => {
    //   const response = formatPayload(data)

    //   this.balanceInfo = response as WSAccountBalance
    //   this.broadcast(`balance`, response)
    // })

    // this.socket.on('updateAssets', (data) => {
    //   const response = formatPayload(data)

    //   // this.allAssets = response as WSChartSettings
    //   // this.broadcast(`chartSettings`, response)
    // })

    this.socket.on('disconnect', (...args: unknown[]) => {
      this.setConnectionState(WSConnectionState.DISCONNECTED)

      this.disconnect()
      this.stopHeartbeat()

      console.log(`Disconnected:`, args)
      this.broadcast(`disconnected`, args)
    })

    this.socket.on('connect_error', (...args: unknown[]) => {
      this.setConnectionState(WSConnectionState.ERROR)

      this.disconnect()
      this.stopHeartbeat()

      this.broadcast(`error`, args)
    })

    this.socket.onAny((event: string, ...args: unknown[]) => {
      const payload = args.length > 0 ? args[0] : args
      const data = formatPayload(payload)

      this.broadcast(event, data)
    })
  }

  /**
   * Send events to renderer via IPC
   * @param event Event name
   * @param data Event data
   */
  private broadcast(event: string, data?: unknown): void {
    let payload: unknown
    BrowserWindow.getAllWindows().forEach((w) => {
      if (data instanceof Buffer || data instanceof ArrayBuffer) {
        payload = formatPayload(data)
      } else {
        payload = data
      }
      w.webContents.send('ws:event', event, payload)
    })
  }
}

export default WebSocketClient
