# WebSocket Client Documentation

A comprehensive WebSocket client implementation using socket.io-client with configurable authentication and robust error handling.

## Features

- ✅ **Socket.io-client integration** - Built on the reliable socket.io-client library
- ✅ **Configurable authentication** - Flexible auth payload configuration
- ✅ **Environment variable support** - Load configuration from environment variables
- ✅ **TypeScript support** - Full type safety and IntelliSense
- ✅ **Connection state management** - Track connection status with detailed logging
- ✅ **Auto-reconnection** - Configurable reconnection with exponential backoff
- ✅ **Error handling** - Comprehensive error handling and recovery
- ✅ **Event-driven architecture** - Listen for connection events and custom messages

## Installation

The required dependencies are already installed:
- `socket.io-client` - WebSocket client library
- `@types/socket.io-client` - TypeScript definitions

## Quick Start

### Basic Usage

```typescript
import { WebSocketClient } from './services/WebSocketClient';
import { WebSocketConfigManager } from './config/WebSocketConfig';

// Create configuration
const config = WebSocketConfigManager.createConfig({
  serverUrl: 'ws://localhost:3000',
  auth: {
    session: 'your_session_id',
    isDemo: 1,
    uid: '12345',
    platform: 2,
    isFastHistory: true
  }
});

// Create client
const client = new WebSocketClient(config);

// Set up event listeners
client.on('connect', () => {
  console.log('Connected!');
  client.send('message', { text: 'Hello Server!' });
});

client.on('message', (data) => {
  console.log('Received:', data);
});

// Connect
await client.connect();
```

### Environment Configuration

Create a `.env` file:

```env
WEBSOCKET_SERVER_URL=ws://localhost:3000
WEBSOCKET_SESSION=your_session_id
WEBSOCKET_IS_DEMO=1
WEBSOCKET_UID=12345
WEBSOCKET_PLATFORM=2
WEBSOCKET_IS_FAST_HISTORY=true
WEBSOCKET_TIMEOUT=5000
WEBSOCKET_AUTO_RECONNECT=true
WEBSOCKET_MAX_RECONNECT_ATTEMPTS=5
WEBSOCKET_RECONNECT_DELAY=1000
```

Then use environment-based configuration:

```typescript
const config = WebSocketConfigManager.createConfig();
const client = new WebSocketClient(config);
```

## Configuration

### Authentication Payload

The authentication payload follows this structure:

```typescript
interface AuthPayload {
  session: string;      // Session identifier
  isDemo: number;       // Demo mode (0 = false, 1 = true)
  uid: string;          // User identifier
  platform: number;    // Platform identifier
  isFastHistory: boolean; // Fast history flag
}
```

### WebSocket Configuration

```typescript
interface WebSocketConfig {
  serverUrl: string;                    // Server URL to connect to
  auth: AuthPayload;                    // Authentication payload
  timeout?: number;                     // Connection timeout (default: 5000ms)
  autoReconnect?: boolean;              // Auto-reconnect (default: true)
  maxReconnectAttempts?: number;        // Max reconnect attempts (default: 5)
  reconnectDelay?: number;              // Reconnect delay (default: 1000ms)
}
```

## API Reference

### WebSocketClient

#### Constructor
```typescript
new WebSocketClient(config: WebSocketConfig)
```

#### Properties
- `state: ConnectionState` - Current connection state (readonly)

#### Methods

##### `connect(): Promise<void>`
Connect to the WebSocket server.

##### `disconnect(): void`
Disconnect from the WebSocket server.

##### `send(event: string, data?: any): void`
Send a message to the server.

##### `on<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void`
Listen for events.

##### `off<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void`
Remove event listener.

##### `isConnected(): boolean`
Check if currently connected.

#### Events

- `connect` - Connection established
- `disconnect` - Connection lost
- `error` - Connection error
- `reconnect` - Reconnection successful
- `reconnect_failed` - Reconnection failed
- `message` - Custom message received

### WebSocketConfigManager

#### Static Methods

##### `createConfig(overrides?: Partial<WebSocketConfig>): WebSocketConfig`
Create a complete configuration with defaults and environment variables.

##### `loadFromEnvironment(): Partial<WebSocketConfig>`
Load configuration from environment variables.

##### `validateConfig(config: WebSocketConfig): { isValid: boolean; errors: string[] }`
Validate configuration and return validation results.

##### `generateEnvExample(): string`
Generate example environment file content.

## Connection States

```typescript
enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}
```

## Examples

### React Component Example

See `src/renderer/src/examples/WebSocketExample.tsx` for a complete React component that demonstrates:
- Configuration management
- Connection controls
- Message sending/receiving
- Real-time status updates

### Simple Usage Examples

See `src/renderer/src/examples/simple-usage.ts` for various usage patterns:
- Basic usage
- Configuration manager usage
- Environment-based configuration
- Custom authentication scenarios
- Error handling

## Error Handling

The client provides comprehensive error handling:

```typescript
client.on('error', (error) => {
  console.error('WebSocket error:', error.message);
  // Handle error (show notification, retry, etc.)
});

client.on('reconnect_failed', () => {
  console.error('Failed to reconnect after maximum attempts');
  // Handle permanent connection failure
});

try {
  await client.connect();
} catch (error) {
  console.error('Initial connection failed:', error);
  // Handle initial connection failure
}
```

## Best Practices

1. **Always validate configuration** before creating the client
2. **Set up error handlers** before connecting
3. **Use environment variables** for sensitive configuration
4. **Handle reconnection scenarios** gracefully
5. **Clean up connections** when components unmount
6. **Log connection state changes** for debugging

## Troubleshooting

### Common Issues

1. **Connection timeout**: Increase the `timeout` value in configuration
2. **Authentication failed**: Verify the auth payload structure and values
3. **Reconnection issues**: Check `maxReconnectAttempts` and `reconnectDelay` settings
4. **Environment variables not loaded**: Ensure proper environment setup

### Debug Logging

The client provides detailed console logging for:
- Connection state changes
- Message sending/receiving
- Error conditions
- Reconnection attempts

Enable debug mode by checking browser console for WebSocket-related logs.
