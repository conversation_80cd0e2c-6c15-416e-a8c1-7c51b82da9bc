import { AccountType } from './enums'

declare global {
  interface WSCallback {
    (...args: unknown[]): void
  }

  interface WSAccountBalance {
    balance: number
    isDemo: AccountType
  }

  interface WSAuthPayload {
    session: string
    isDemo: AccountType
    uid: number
    platform: number
    isFastHistory: boolean
  }

  interface WebSocketConfig {
    auth: WSAuthPayload
  }

  interface WebSocketEvents {
    /** Connection established */
    connect: () => void
    /** Connection lost */
    disconnect: (reason: string) => void
    /** Connection error */
    error: (error: Error) => void
    /** Reconnection attempt */
    reconnect: (attemptNumber: number) => void
    /** Reconnection failed */
    reconnect_failed: () => void
    /** Custom message received */
    message: (data: unknown) => void
    successupdateBalance: (data: WSAccountBalance) => void
    updateAssets: (data: WSChartSettings) => void
  }

  interface WSChartSettings {
    chartId: string
    chartType: number
    chartPeriod: number
    candlesTimer: boolean
    symbol: string
    demoDealAmount: number
    liveDealAmount: number
    enabledTradeMonitor: boolean
    enabledRatingWidget: boolean
    isVisible: boolean
    fastTimeframe: number
    enabledAutoscroll: boolean
    enabledGridSnap: boolean
    minimizedTradePanel: boolean
    fastCloseAt: number
    enableQuickAutoOffset: boolean
    quickAutoOffsetValue: number
    showArea: boolean
    percentAmount: number
  }
}
